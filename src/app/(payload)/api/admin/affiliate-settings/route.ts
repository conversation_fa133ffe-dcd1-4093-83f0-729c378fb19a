import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from '@/payload-config/getPayloadConfig'

// GET - Fetch affiliate settings with pagination and user filtering for admin
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1') || 1
    const limit = parseInt(searchParams.get('limit') || '10') || 10
    const userId = searchParams.get('userId')

    const payload = await getPayload()

    // Build where clause
    const where: any = {}

    if (userId) {
      where.affiliateUser = {
        equals: parseInt(userId),
      }
    }

    const affiliateSettings = await payload.find({
      collection: 'affiliate-settings',
      where,
      page,
      limit,
      sort: '-createdAt',
      depth: 2, // Include related event and user data
    })

    // Format the response data
    const formattedData = affiliateSettings.docs.map((setting: any) => {
      return {
        id: setting.id,
        name: setting.name,
        isActive: setting.isActive,
        description: setting.description,
        event: setting.event
          ? {
              id: setting.event.id,
              title: setting.event.title,
              slug: setting.event.slug,
              eventLocation: setting.event.eventLocation,
              description: setting.event.description,
            }
          : null,
        affiliateUser: setting.affiliateUser
          ? {
              id: setting.affiliateUser.id,
              email: setting.affiliateUser.email,
              firstName: setting.affiliateUser.firstName,
              lastName: setting.affiliateUser.lastName,
            }
          : null,
        promotions: setting.promotions || [],
        tiers: setting.tiers || [],
        createdAt: setting.createdAt,
        updatedAt: setting.updatedAt,
      }
    })

    return NextResponse.json({
      success: true,
      data: formattedData,
      pagination: {
        page: affiliateSettings.page,
        limit: affiliateSettings.limit,
        totalPages: affiliateSettings.totalPages,
        totalDocs: affiliateSettings.totalDocs,
        hasNextPage: affiliateSettings.hasNextPage,
        hasPrevPage: affiliateSettings.hasPrevPage,
      },
    })
  } catch (error) {
    console.error('Error fetching affiliate settings:', error)

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error. Please try again later.',
      },
      { status: 500 },
    )
  }
}
