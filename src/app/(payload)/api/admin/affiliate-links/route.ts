import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from '@/payload-config/getPayloadConfig'

// GET - Fetch affiliate links with pagination and user filtering for admin
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1') || 1
    const limit = parseInt(searchParams.get('limit') || '10') || 10
    const userId = searchParams.get('userId')
    const status = searchParams.get('status')

    const payload = await getPayload()

    // Build where clause
    const where: any = {}

    if (userId) {
      where.affiliateUser = {
        equals: parseInt(userId),
      }
    }

    if (status && ['active', 'disabled', 'expired'].includes(status)) {
      where.status = { equals: status }
    }

    const affiliateLinks = await payload.find({
      collection: 'affiliate-links',
      where,
      page,
      limit,
      sort: '-createdAt',
      depth: 2, // Include related event and user data
    })

    // Format the response data
    const formattedData = affiliateLinks.docs.map((link: any) => {
      return {
        id: link.id,
        affiliateCode: link.affiliateCode,
        promotionCode: link.promotionCode,
        utmParams: link.utmParams,
        targetLink: link.targetLink,
        status: link.status,
        event: link.event
          ? {
              id: link.event.id,
              title: link.event.title,
              slug: link.event.slug,
              eventLocation: link.event.eventLocation,
              description: link.event.description,
            }
          : null,
        affiliateUser: link.affiliateUser
          ? {
              id: link.affiliateUser.id,
              email: link.affiliateUser.email,
              firstName: link.affiliateUser.firstName,
              lastName: link.affiliateUser.lastName,
            }
          : null,
        createdAt: link.createdAt,
        updatedAt: link.updatedAt,
      }
    })

    return NextResponse.json({
      success: true,
      data: formattedData,
      pagination: {
        page: affiliateLinks.page,
        limit: affiliateLinks.limit,
        totalPages: affiliateLinks.totalPages,
        totalDocs: affiliateLinks.totalDocs,
        hasNextPage: affiliateLinks.hasNextPage,
        hasPrevPage: affiliateLinks.hasPrevPage,
      },
    })
  } catch (error) {
    console.error('Error fetching affiliate links:', error)

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error. Please try again later.',
      },
      { status: 500 },
    )
  }
}
